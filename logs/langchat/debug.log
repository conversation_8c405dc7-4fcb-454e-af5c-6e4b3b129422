2025-09-16 09:51:08,682 [http-nio-8100-exec-9] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-16 09:51:08,690 [http-nio-8100-exec-9] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-16 09:51:08,690 [http-nio-8100-exec-9] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-16 09:51:09,617 [http-nio-8100-exec-9] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 09:51:09,618 [http-nio-8100-exec-9] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_7149931123364979745.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 09:51:19,195 [http-nio-8100-exec-9] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时9427.89730000496ms
2025-09-16 09:51:19,197 [http-nio-8100-exec-9] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-16 09:51:20,224 [http-nio-8100-exec-9] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 09:51:20,224 [http-nio-8100-exec-9] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_3808673153974172018.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 09:51:31,514 [http-nio-8100-exec-9] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时11143.779700011015ms
2025-09-16 09:51:31,517 [http-nio-8100-exec-9] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-16 09:51:32,332 [http-nio-8100-exec-9] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 09:51:32,333 [http-nio-8100-exec-9] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_14582693227570197471.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 09:51:38,714 [http-nio-8100-exec-9] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6257.294600009918ms
2025-09-16 09:51:38,715 [http-nio-8100-exec-9] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-16 09:58:32,250 [http-nio-8100-exec-7] INFO  [c.t.langchat.ai.core.provider.ProviderListener] ProviderListener.java:40 - refresh provider beans begin......
2025-09-16 09:58:32,253 [http-nio-8100-exec-7] INFO  [c.t.langchat.ai.core.provider.ProviderListener] ProviderListener.java:42 - refresh provider beans success......
2025-09-16 09:58:32,570 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,570 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,570 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,570 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,570 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,570 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,571 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,571 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,571 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,571 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 09:58:32,571 [task-4] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,484 [http-nio-8100-exec-5] INFO  [c.t.langchat.ai.core.provider.ProviderListener] ProviderListener.java:40 - refresh provider beans begin......
2025-09-16 10:12:58,485 [http-nio-8100-exec-5] INFO  [c.t.langchat.ai.core.provider.ProviderListener] ProviderListener.java:42 - refresh provider beans success......
2025-09-16 10:12:58,817 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=0873ec98f91054ab387fbac3dbf3c1bf, type=CHAT, model=通义千问VL-OCR, provider=Q_WEN, name=通义千问VL-OCR, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-7291776f57e7486ebad82dcb3bf47c6e, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,818 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,819 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,819 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:12:58,819 [task-5] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,488 [http-nio-8100-exec-10] INFO  [c.t.langchat.ai.core.provider.ProviderListener] ProviderListener.java:40 - refresh provider beans begin......
2025-09-16 10:14:29,489 [http-nio-8100-exec-10] INFO  [c.t.langchat.ai.core.provider.ProviderListener] ProviderListener.java:42 - refresh provider beans success......
2025-09-16 10:14:29,806 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=0873ec98f91054ab387fbac3dbf3c1bf, type=CHAT, model=通义千问VL-OCR, provider=Q_WEN, name=通义千问VL-OCR-USE, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-7291776f57e7486ebad82dcb3bf47c6e, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,806 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,806 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,806 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:29,807 [task-6] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-16 10:14:48,732 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-16 10:14:48,734 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-16 10:14:48,734 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-16 10:14:49,709 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:14:49,709 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_14017637128576378081.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:14:59,108 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时9263.131999999285ms
2025-09-16 10:14:59,111 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-16 10:15:00,132 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:15:00,132 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_17003721101873717905.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:15:11,170 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时10890.083400011063ms
2025-09-16 10:15:11,172 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-16 10:15:12,054 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:15:12,054 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_13386976360009024692.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:15:18,860 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6678.861800014973ms
2025-09-16 10:15:18,862 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-16 10:16:39,536 [http-nio-8100-exec-2] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-16 10:16:39,538 [http-nio-8100-exec-2] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-16 10:16:39,538 [http-nio-8100-exec-2] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-16 10:16:40,501 [http-nio-8100-exec-2] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:16:40,502 [http-nio-8100-exec-2] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_16816290981592488087.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:16:50,427 [http-nio-8100-exec-2] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时9790.327100008726ms
2025-09-16 10:16:50,429 [http-nio-8100-exec-2] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-16 10:16:51,438 [http-nio-8100-exec-2] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:16:51,438 [http-nio-8100-exec-2] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_4102284454520102077.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:17:02,395 [http-nio-8100-exec-2] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时10813.45480003953ms
2025-09-16 10:17:02,397 [http-nio-8100-exec-2] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-16 10:17:03,190 [http-nio-8100-exec-2] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:17:03,191 [http-nio-8100-exec-2] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_16347484341863097101.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:17:09,731 [http-nio-8100-exec-2] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6412.261699974537ms
2025-09-16 10:17:09,733 [http-nio-8100-exec-2] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-16 10:18:22,679 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-16 10:18:22,681 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-16 10:18:22,681 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-16 10:18:23,604 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:18:23,605 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_14956875477849499477.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:18:33,257 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时9507.48939999938ms
2025-09-16 10:18:33,259 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-16 10:18:34,262 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:18:34,262 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_1609355618110378441.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:18:45,476 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时11064.228799968958ms
2025-09-16 10:18:45,478 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-16 10:18:46,318 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:18:46,319 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_4991075574638354062.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:18:52,806 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6362.619199991226ms
2025-09-16 10:18:52,807 [http-nio-8100-exec-4] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-16 10:19:42,736 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-16 10:19:42,738 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-16 10:19:42,738 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-16 10:19:43,685 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:19:43,686 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_10319956791321251763.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:19:54,209 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时10383.293600022793ms
2025-09-16 10:19:54,211 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-16 10:19:55,253 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:19:55,254 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_15377789284735967276.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:20:07,481 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时12079.412800014019ms
2025-09-16 10:20:07,483 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-16 10:20:08,282 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:20:08,282 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_15128806267187685155.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:20:14,804 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6396.991199970245ms
2025-09-16 10:20:14,806 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-16 10:22:33,809 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-16 10:22:33,811 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-16 10:22:33,811 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-16 10:22:34,761 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:22:34,762 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_14892552076517009139.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:22:45,999 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时11095.850299984217ms
2025-09-16 10:22:46,001 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-16 10:22:47,111 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:22:47,111 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_7108174056946282435.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:22:58,773 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时11511.702199965715ms
2025-09-16 10:22:58,775 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-16 10:22:59,580 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-16 10:22:59,580 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_2726721102442583649.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-16 10:23:06,318 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6611.915199995041ms
2025-09-16 10:23:06,321 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-16 10:46:00,427 [http-nio-8100-exec-4] INFO  [cn.tycoding.langchat.auth.endpoint.AuthEndpoint] AuthEndpoint.java:93 - ====> login success，token=b0fb7399-bd76-492e-b689-4185338d4cbc
